<template>
  <footer class="border-t border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800">
    <CoreBacktoTop />
    <CoreCookieConsent />
    <div
      class="px-2 sm:px-5 py-2 sm:py-5 mb-10 mx-auto flex md:items-center lg:items-start md:flex-row md:flex-nowrap flex-wrap flex-col">
      <div class="w-64 sm:flex-shrink-0 mt-12 sm:mt-0 px-5">
        <h3 class="tracking-widest font-semibold text-start text-lg uppercase mb-3 text-zinc-800 dark:text-zinc-100">
          Social Media
        </h3>
        <div class="grid grid-cols-4">
          <a class="bg-blue-600 rounded-full text-white flex cursor-pointer items-center justify-center w-10 h-10">
            <Icon icon="ri:facebook-fill" class="w-6 h-6" />
          </a>
          <a class="bg-pink-600 rounded-full text-white flex cursor-pointer items-center justify-center w-10 h-10">
            <Icon icon="bi:instagram" class="w-6 h-6" />
          </a>
          <a class="bg-gray-400 rounded-full text-white flex cursor-pointer items-center justify-center w-10 h-10">
            <Icon icon="devicon:twitter" class="w-6 h-6" />
          </a>
          <a class="bg-red-600 rounded-full text-white flex cursor-pointer items-center justify-center w-10 h-10">
            <Icon icon="logos:youtube-icon" class="w-6 h-6" />
          </a>
        </div>
        <div>
          <h3
            class="tracking-widest font-semibold text-start text-lg uppercase mb-3 mt-5 text-zinc-800 dark:text-zinc-100">
            Download App
          </h3>
          <div class="flex items-center space-x-3">
            <a href="#" target="_blank">
              <img src="@/assets/images/app-store.png" alt="app-store" class="w-28 h-10 object-contain bg-black" />
            </a>
            <a href="#" target="_blank">
              <img src="@/assets/images/google-play.png" alt="google-play" class="w-28 h-10 object-contain bg-black" />
            </a>
          </div>
        </div>
      </div>

      <div class="flex-grow flex flex-wrap md:pr-20 -mb-10 md:text-left text-center order-first">
        <div class="lg:w-1/4 md:w-1/2 w-full px-4">
          <h2 class="tracking-widest font-semibold text-start text-lg mb-2 uppercase text-zinc-800 dark:text-zinc-100">
            QUICK LINKS
          </h2>
          <nav class="list-none mb-2 flex flex-col items-start space-y-2">
            <NuxtLink to="/events">
              <a class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
                Latest Events
              </a>
            </NuxtLink>
            <NuxtLink to="/dashboard">
              <a class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
                Event Managemenent
              </a>
            </NuxtLink>
            <NuxtLink to="/events?tag=trending">
              <a class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
                Trending Events
              </a>
            </NuxtLink>
            <NuxtLink to="/vendors">
              <a class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
                Vendors
              </a>
            </NuxtLink>
            <NuxtLink to="/venues">
              <a class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
                Venues
              </a>
            </NuxtLink>
            <NuxtLink to="/my-profile?tab=Tickets">
              <a class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
                Event Tickets
              </a>
            </NuxtLink>
          </nav>
        </div>

        <div class="lg:w-1/4 md:w-1/2 w-full px-4">
          <h2 class="tracking-widest font-semibold text-left text-lg mb-2 uppercase text-zinc-800 dark:text-zinc-100">
            INFORMATION
          </h2>
          <nav class="list-none mb-2 flex flex-col items-start space-y-2">
            <NuxtLink to="/become-host"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Become Host
            </NuxtLink>
            <NuxtLink to="/about"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              About EventaHub
            </NuxtLink>
            <NuxtLink to="/contact-us"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Contact Us
            </NuxtLink>
            <NuxtLink to="/vendors/request"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Become Vendor
            </NuxtLink>
            <NuxtLink to="/refund-policy"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Refund Policy
            </NuxtLink>
          </nav>
        </div>

        <div class="lg:w-1/4 md:w-1/2 w-full px-4">
          <h2 class="tracking-widest font-semibold text-left text-lg mb-2 uppercase text-zinc-800 dark:text-zinc-100">
            CATEGORIES
          </h2>
          <nav class="list-none mb-10 flex flex-col space-y-2 text-left">
            <NuxtLink v-for="category in categories" v-bind:key="category" :to="`/events?category=${category}`"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              {{ category }}
            </NuxtLink>
          </nav>
        </div>

        <div class="lg:w-1/4 md:w-1/2 w-full px-4">
          <h2 class="tracking-widest text-left font-semibold text-lg mb-2 uppercase text-zinc-800 dark:text-zinc-100">
            LEGAL & SUPPORT
          </h2>
          <nav class="list-none mb-2 flex flex-col space-y-2 text-left">
            <NuxtLink to="/terms-usage-policy"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Terms and Usage Policy
            </NuxtLink>
            <NuxtLink to="/privacy-policy"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Privacy and Cookies Policy
            </NuxtLink>
            <NuxtLink to="/copyright-information"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Copyright Information
            </NuxtLink>
            <NuxtLink to="/help-center"
              class="text-gray-600 dark:text-gray-300 hover:underline hover:text-sky-500 transition duration-150">
              Help Center
            </NuxtLink>
          </nav>
        </div>
      </div>
    </div>

    <div class="bg-gray-50 dark:bg-zinc-900 border-t border-zinc-200 dark:border-zinc-700">
      <div class="mx-auto py-2 sm:py-4 px-2 sm:px-5 flex flex-wrap flex-col sm:flex-row items-center justify-between">
        <p class="text-gray-500 dark:text-gray-400 text-center sm:text-left">
          &copy; {{ new Date().getFullYear() }} EventaHub — Developed with <span class="text-red-600">&hearts;</span> in Malawi. Product of
          <a href="https://brainytechnologies.mw" rel="noopener noreferrer"
            class="text-sky-500 hover:text-sky-500 hover:underline transition duration-150 ml-1" target="_blank">Brainy
            Technologies</a>
        </p>
        <div class="flex items-center text-gray-500 dark:text-gray-400 mt-2 sm:mt-0">
          <Icon icon="heroicons:globe-alt" class="w-4 h-4 mr-2" />
          <span class="text-sm">English</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script lang="ts" setup>
const categories = ref<string[]>([
  'Music Concerts',
  'Workshops',
  'Conferences',
  'Festivals',
  'Sports Events',
  'Charity Events',
]);
</script>

<style lang="css" scoped></style>
