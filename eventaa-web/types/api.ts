import type { EventItem } from ".";
import type { User } from "./user";

export interface ApiResponse<T> {
    data: T;
    status: number;
    message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
    meta: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
    };
}

export interface ApiError {
    message: string;
    status: number;
    errors?: Record<string, string[]>;
    email_verification_required?: boolean;
    email?: string;
}

export interface EventsResponse {
    events: {
        data: EventItem[];
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
    };
    message?: string;
}

export interface AuthResponse {
    message: string
    user?: User
    token?: string
    refresh_token?: string
    email_verification_required?: boolean
    email?: string
}

export interface AuthState {
    user: User | null
    token: string | null
    refresh_token: string | null
    message: string | null
    isAuthenticated: boolean
}

export interface GenericResponse {
    message: string;
}

export interface Notification {
    id: number;
    type: string;
    data: any;
    read_at: string | null;
    created_at: string;
    updated_at: string;
}

export interface NotificationsResponse {
    data: Notification[];
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    prev_page_url: string;
    next_page_url: string;
}

export interface Setting {
    id: number;
    key: string;
    name: string;
    description: string;
    type: string;
    enabled: boolean;
    user_enabled: boolean;
    created_at: string;
    updated_at: string;
}

export interface SettingsResponse {
    message: string;
    settings: Setting[];
}

export interface PasswordResetRequest {
    password: string;
    confirm_password: string;
}

export interface DeactivateAccountResponse {
    message: string;
}

export interface Withdrawal {
    id: number;
    reference: string;
    amount: number;
    fee: number;
    net_amount: number;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    payment_method: string;
    payment_details: Record<string, any>;
    created_at: string;
    processed_at?: string;
    paychangu_reference?: string;
    admin_notes?: string;
}

export interface WithdrawalResponse {
    data: Withdrawal[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

export interface WithdrawalBalance {
    available_balance: number;
    pending_balance: number;
    total_earned: number;
    total_withdrawn: number;
    platform_fees_collected: number;
    last_payout_at: string | null;
}

export interface WithdrawalData {
    balance: WithdrawalBalance;
    withdrawals: WithdrawalResponse;
    minimum_withdrawal: number;
    withdrawal_fee_percentage: number;
}

export interface WithdrawalRequest {
    amount: number;
    payment_method: string;
    payment_details: Record<string, any>;
    recaptcha_token: string;
}
